

# **面向鲁棒无线信道分解的SVDNet框架：一个基于第一性原理的性能驱动优化方案**

**摘要**

本报告针对“AI使能的无线鲁棒SVD算子”挑战赛，提出了一套对SVDNet框架的全面优化策略。在现有方案坚实的几何感知U-Net架构基础上，本报告引入了一系列相互关联的增强措施，旨在将方案的性能、效率与鲁棒性提升至全新高度。关键创新点包括：(1) 对骨干网络进行严格的再评估，并建议采用在任务特定输入维度下具有更优乘加运算（MACs）效率的架构，如MobileNetV3，以争夺在AE-MACs帕累托前沿上的主导地位；(2) 通过引入基于Householder变换的参数化方法，替代原有的缩放凯莱变换，以增强几何输出层的数值稳定性并提升GPU并行化效率；(3) 训练范式的革新，融合基于不确定性的动态损失加权机制，以自动平衡重构与正交性目标，并实施结合物理先验变换与生成式信道合成的双管齐下数据增强策略；(4) 引入一种基于第一性原理的集成聚合方法，即在斯蒂费尔流形上计算几何中位数，以确保快照集成模型输出的酉性。最终形成的“SVDNet v2.0”蓝图，代表了一个为在竞赛的AE-MACs评估体系中取得决定性优势而精心设计的、高度整合的、技术前沿的解决方案。

## **1\. SVDNet框架的战略性重估**

### **1.1. 对现有坚实基础的认可**

初始方案（1）因其基于第一性原理的深刻洞察而备受肯定。该方案准确地将竞赛核心挑战识别为一个在几何流形上的双目标优化问题，这构成了后续所有优化的坚实基础。具体而言，其优势体现在以下几个方面：

* **正确的几何视角**：方案明确指出，SVD的输出矩阵U和V必须是酉矩阵，其构成的空间为斯蒂费尔流形（Stiefel Manifold）1。这一认知至关重要，因为它将问题从一个无约束的回归任务，正确地定义为一个具有严格几何约束的映射学习任务。  
* **先进的架构选择**：采用U-Net结构来保留信道矩阵的精细空间信息，并选择现代卷积神经网络（CNN）如ConvNeXt作为骨干，体现了对当前深度学习架构发展趋势的深刻理解 1。  
* **原则性的约束处理**：通过引入可微分的缩放凯莱变换（Scaled Cayley Transform）来直接构造酉矩阵输出，从根本上解决了正交性约束问题。这种“构造即满足”的方法，远优于依赖惩罚项的间接、不稳定的优化方法，直接回应了竞赛对方案可行性的核心关切 1。

### **1.2. 明确优化目标：在帕累托前沿上寻求最优解**

竞赛的评价体系为优化指明了清晰且严苛的方向。最终排名首先依据近似误差（Approximation Error, AE）进行排序，然后在划分的性能档位内，根据计算复杂度（以MACs衡量）进行二次排序 1。这一规则揭示了问题的本质：解决方案必须在模型的预测精度与计算效率之间寻求帕累托最优。

这意味着，单纯追求极致的低AE或极致的低MACs都无法在竞赛中胜出。优化的核心目标可以被精确地定义为：**在给定的计算资源预算（MACs）内，最大化地降低近似误差（AE）**。分档排序机制进一步暗示，AE的显著降低比复杂度的边际减少更具价值，因为它能将方案提升至更高的性能档位。然而，一旦进入同一档位，一个计算量庞大但AE仅略优于轻量级对手的模型，将因其高昂的计算成本而处于劣势。

基于此，本优化报告的目标被设定为：

* **主要目标**：将SVDNet v2.0方案推向AE-MACs二维评估平面上一个更具优势的帕累to最优点，即以更低的MACs实现同等甚至更低的AE。  
* **次要目标**：  
  * 通过引入自动化、动态的损失平衡机制，提升训练过程的稳定性与收敛速度。  
  * 针对竞赛描述中提及的“非理想因素”（如噪声、定时偏移等）1，通过设计全面的数据增强与推理策略，最大化模型的泛化能力与鲁棒性。  
  * 确保所有提出的优化组件均具备在PyTorch框架下实现的可行性，并严格遵守竞赛禁止使用SVD、QR分解等高相关算子的规定 1。

## **2\. 优化架构核心：超越ConvNeXt，追求极致的效率-性能前沿**

### **2.1. 对ConvNeXt-UNet骨干网络的批判性分析**

原有方案选择ConvNeXt-T作为U-Net的编码器骨干，其理由是ConvNeXt架构成功地将Transformer的设计精髓融入CNN，在保持卷积固有效率的同时实现了卓越的性能 1。相关基准测试也证实了ConvNeXt作为一种强大的特征提取器的地位 2。

然而，一个关键的背景因素必须被考虑：绝大多数高性能CNN架构，包括ConvNeXt和EfficientNetV2，其设计和优化主要针对大规模图像分类任务，如ImageNet，其输入图像尺寸通常为224x224或更大 2。本次竞赛的输入数据是一个

64×64×2的张量，可被视为一个64×64的双通道“图像”1。对于这样的小尺寸输入，那些为极端效率而设计的轻量级架构，可能在“单位算力性能”上提供一个更优的MACs与AE的权衡。

此外，近期的研究（如ConvFirst）揭示，理论上的浮点运算次数（FLOPs）或MACs并不总能直接转化为实际的推理速度 5。通过采用“块融合”（block-fusion）等技术优化GPU的计算效率，可以在同等精度下实现数倍于ConvNeXt的速度提升。这表明，选择一个不仅理论MACs低，而且其结构更适合现代并行计算硬件的骨干网络，对于训练效率和最终性能都至关重要。

### **2.2. 轻量级架构的比较研究：MobileNetV3的崛起**

为了在竞赛特定的输入规模下找到最佳的效率-性能平衡点，有必要对其他顶级的轻量化架构进行评估。

* **MobileNetV3** 6：该架构是为移动设备（即资源受限环境）从头设计的。其核心构件——包括使用深度可分离卷积的倒置残差块、用于通道注意力机制的Squeeze-and-Excitation (SE)模块，以及计算成本极低的h-swish激活函数——都是为了在最小化计算量的同时最大化模型精度 7。它在较小数据集上的优异表现 9 及其对低延迟的专注，使其成为本任务的理想候选者。  
* **EfficientNetV2** 2：以其通过神经架构搜索（NAS）实现的业界领先的精度-效率权衡而闻名。相较于V1，它在早期阶段引入了Fused-MBConv模块以提升训练速度 4。虽然性能强大，但其模型复杂度可能仍然偏高，在  
  64×64这个尺度上，其相对于MobileNetV3的优势并不确定，需要通过实验验证。  
* **ShuffleNetV2** 9：这是另一款极致高效的架构，利用通道混洗（channel shuffle）和分组卷积来降低计算量。它通常拥有最低的FLOPs，但精度有时会略逊于MobileNetV3 9。

竞赛的双目标排名机制（AE优先，同档位内MACs优先）引导了一种精明的策略选择。一个在AE上略有提升但MACs大幅增加的模型，只有在能够跨越性能档位时才是有价值的。反之，如果一个MACs显著更低的模型能够达到与高复杂度模型相近的AE，那么它将在同档位竞争中获得决定性优势。从ConvNeXt转向MobileNetV3，正是基于这样一种战略考量：押注一个更精简的模型能够在本任务特定的数据和规模下，达到与重量级模型同等的AE水平，从而在关键的次要指标——计算复杂度上胜出。这不仅仅是选择一个“替代品”，而是针对竞赛规则进行的一次主动的、以获胜为目的的战略调整。

### **2.3. 行动方案与理论依据**

**推荐方案**：在U-Net架构中，采用**MobileNetV3-Large**作为编码器骨干，替换原有的ConvNeXt-T。

**理论依据**：MobileNetV3是为优化资源受限设备上的“精度-延迟”曲线而专门设计的 6。其构件（如h-swish激活函数、SE模块、倒置残差结构）均体现了对计算效率的极致追求 7。据此推断，对于

64×64的输入尺寸，MobileNetV3相比ConvNeXt-T，有望在提供可比甚至更优AE的同时，显著降低MACs总量，从而将整个解决方案推向竞赛评估体系中一个更具统治力的位置。U-Net的跳跃连接结构将继续保留，以确保从编码器到解码器的多尺度特征融合，这对于需要精细空间信息的SVD预测任务至关重要。

为了更清晰地论证此选择，下表对几个候选骨干架构进行了对比分析。

**表 2.1: 轻量级骨干网络架构对比分析**

| 架构 | 关键特性 | 预估MACs (64x64输入) | ImageNet Top-1 准确率 (特征提取能力代理指标) | 优点 (针对本任务) | 缺点 (针对本任务) |
| :---- | :---- | :---- | :---- | :---- | :---- |
| ConvNeXt-T 1 | 现代化CNN，大卷积核，层归一化 | 中等 | 82.5% 2 | 性能强大，结构稳健 | 针对小尺寸输入可能计算过剩，效率非最优 |
| **MobileNetV3-Large (推荐)** | 深度可分离卷积，SE模块，h-swish激活函数 | **较低** | 75.3% 2 | **极致的计算效率**，专为低延迟设计，完美契合MACs评价指标 | 原始特征提取能力略低于ConvNeXt，需依赖U-Net结构弥补 |
| EfficientNetV2-S 4 | 神经架构搜索(NAS)，Fused-MBConv | 中-高 | 84.2% 2 | 顶尖的精度-效率权衡 | 复杂度可能依然偏高，在小尺寸输入上的优势不确定 |
| ShuffleNetV2 9 | 通道混洗，分组卷积 | 极低 | \- | 理论MACs最低 | 精度可能略有不足，相比MobileNetV3风险更高 |

分析显示，MobileNetV3-Large在提供极具竞争力的计算复杂度的同时，其强大的特征提取能力（由ImageNet准确率佐证）足以胜任本任务。它是实现“顶级AE档位内的最低MACs”这一战略目标的最优选择。

## **3\. 强化几何完整性：先进的可微分酉矩阵参数化方法**

### **3.1. 评估缩放凯莱变换的优与劣**

原方案中选择缩放凯莱变换（Scaled Cayley Transform）1是一个非常出色且符合第一性原理的决策。它将一个无约束的斜埃尔米特矩阵

A通过映射$Q=(I-A)D(I+A)^{-1}$确定性地变换为酉矩阵$Q$，从而在架构层面保证了输出的几何正确性。

然而，这一优雅的数学形式背后隐藏着一个潜在的工程挑战：**矩阵求逆**。torch.linalg.inv操作虽然可微，但在训练过程中，如果矩阵$(I+A)$变得病态（ill-conditioned）或接近奇异，求逆操作可能会导致数值溢出（NaN或Inf），从而中断训练。这在float32精度下尤为突出。原方案中提出的使用float64双精度计算作为规避措施是可行的，但这会使内存占用和计算时间加倍，可能对训练效率产生不利影响 1。

### **3.2. 备选方案：基于Householder变换的参数化**

一个强大且数值上更为稳健的构造任意酉矩阵的方法是利用**Householder变换**（或称Householder反射）11。任何一个

d×d的酉矩阵U都可以表示为至多d个Householder矩阵Hi​的乘积：U=H1​H2​⋯Hd​。其中，每个Householder矩阵由一个向量vi​定义：Hi​=I−2vi​viH​/∥vi​∥2。

**核心优势**：

1. **数值稳定性**：该方法完全避免了矩阵求逆，其核心运算是向量外积和矩阵-向量乘法，这些操作在数值上通常比求逆稳定得多。  
2. **GPU并行化潜力**：直接按顺序计算d个Householder矩阵的乘积在GPU上是低效的，因为它构成了一个长的依赖链。然而，**FastH算法** 12专门解决了这个问题。它通过将Householder矩阵分组，并利用数学技巧将组内乘积预计算为  
   I−2WYT的形式，从而将$O(d)$的串行内积操作转化为$O(d/m \+ m)$（m为批大小）的串行矩阵-矩阵乘法，极大地提升了GPU的并行计算效率 12。  
3. **完全可微**：整个构造过程对于底层的Householder向量vi​是完全可微的，因此可以无缝地集成到端到端的神经网络训练中 12。

选择参数化方法不仅仅是一个数学问题，它更体现了对数值分析和硬件架构之间相互作用的深刻理解。凯莱变换在数学上简洁，但其求逆步骤是一个潜在的数值“阿喀琉斯之踵”，且其结构并非为并行硬件优化。相反，Householder变换，特别是与FastH算法结合时，是一个典型的工程权衡范例：它用一个更复杂的、但对硬件友好的算法，换取了更高的数值鲁棒性和实际计算速度。在深度学习竞赛中，训练的稳定性和速度至关重要，这种硬件感知的选择往往是更优的。

### **3.3. 行动方案与理论依据**

**推荐方案**：将原方案中的缩放凯莱变换层替换为基于**Householder变换的参数化层**，并依据FastH算法 12 的思想进行高效的并行化实现。

**理论依据**：此项更改旨在同时提升几何输出阶段的**鲁棒性**和**训练速度**。通过消除矩阵求逆，可以从根本上规避训练过程中可能出现的数值不稳定问题。通过采用一种为GPU高度优化的并行构造方法，可以加速前向和反向传播过程，这对于可能需要大量训练周期的复杂任务来说是至关重要的。

**表 3.1: 酉矩阵参数化方法对比**

| 方法 | 数学形式 | 可微参数 | 核心计算步骤 | 数值稳定性 | GPU并行性 | 优点 | 缺点 |
| :---- | :---- | :---- | :---- | :---- | :---- | :---- | :---- |
| 缩放凯莱变换 1 | Q=(I−A)D(I+A)−1 | 斜埃尔米特矩阵A的元素 | 矩阵求逆 | **中等** (依赖于$(I+A)$的条件数) | 低 (求逆操作难以大规模并行) | 数学形式简洁 | 存在数值不稳定风险，求逆可能成为瓶颈 |
| **Householder变换 (FastH)** 12 | $U \= \\prod\_{i=1}^d (I \- 2v\_i v\_i^H / \\|v\_i\\|^$ | Householder向量vi​ | 向量外积，矩阵-矩阵乘法 | **高** (无矩阵求逆) | **高** (FastH算法专为GPU并行优化) | **数值稳定，训练速度快** | 算法实现比凯莱变换更复杂 |

## **4\. 训练范式的革新：动态、数据驱动与鲁棒性的系统化构建**

### **4.1. 从静态到动态：利用不确定性加权损失实现任务自动平衡**

原方案提出的多部分损失函数Ltotal​=λrec​Lrec​+λorthoU​LorthoU​+λorthoV​LorthoV​ 1 依赖于对超参数$\\lambda$的手动调整。这是一个耗时且次优的过程，因为重构与正交性任务之间的最佳平衡点可能在训练的不同阶段发生变化。

将此问题视为一个多任务学习（Multi-task Learning, MTL）问题，可以引入更先进的动态加权策略。**不确定性加权法（Uncertainty Weighting）** 13 提供了一种基于概率推断的、有原则的解决方案。该方法将多任务损失函数构建为一个联合概率的最大似然估计问题，其最终形式为：

$$ L\_{\\text{total}} \= \\frac{1}{2\\sigma\_{\\text{rec}}^2} L\_{\\text{rec}} \+ \\frac{1}{2\\sigma\_U^2} L\_{\\text{orthoU}} \+ \\frac{1}{2\\sigma\_V^2} L\_{\\text{orthoV}} \+ \\log(\\sigma\_{\\text{rec}}) \+ \\log(\\sigma\_U) \+ \\log(\\sigma\_V) $$  
其中，σrec​, σU​, σV​是三个可学习的标量参数，分别代表模型对重构任务、U矩阵正交性任务和V矩阵正交性任务的预测不确定性（或噪声水平）。在训练过程中，网络会自动学习这些$\\sigma值。如果某个任务的损失较大（即不确定性高），网络会通过增大学习到的\\sigma$值来降低该任务损失项的权重（$1/(2\\sigma^2)项），同时\\log(\\sigma)$正则项会防止权重无限增大。这种机制使得网络能够智能地、动态地平衡不同任务的贡献，将学习资源更多地分配给它“更有把握”的任务，从而实现自动化的损失加权。  
**实施方案**：在PyTorch中，将这三个$\\sigma$参数定义为nn.Parameter，并将它们与模型的主体参数一同加入优化器进行学习 15。这种方法已被证明优于手动调参和其他动态加权方法（如DWA）18。

### **4.2. 最大化泛化能力：双管齐下的数据增强策略**

竞赛要求模型在三个不同场景中均表现良好，并能应对各种非理想信道因素 1，这使得数据增强成为提升模型鲁棒性和泛化能力的关键环节，但在原方案中未被充分探讨。为此，提出一个结合物理先验和生成式方法的双管齐下策略。

策略一：基于物理先验的在线增强（低成本，高针对性）  
这些变换在训练时对每个批次的数据进行即时处理，旨在模拟无线信道中常见的物理扰动。

* **随机相位/幅度抖动**：对输入的信道矩阵H的每个元素乘以一个小的随机复数（模接近1，相位随机），以模拟硬件振荡器漂移和放大器非线性等硬件不完美性 20。  
* **加性高斯白噪声（AWGN）注入**：向输入信道矩阵中加入不同信噪比（SNR）水平的复高斯噪声。这直接训练模型对噪声的鲁棒性，是应对竞赛中“非理想因素”最直接的方法 1。  
* **“气泡-平移”（Bubble-Shift）增强**：该技术 24 通过在频域上施加一个线性相移来模拟时域上的循环平移，这能有效模拟由于定时误差（如竞赛背景中提到的“定时提前”）引起的信道脉冲响应偏移 1。

策略二：基于生成式模型的离线信道合成（高影响力，提升泛化）  
官方提供的训练数据集虽然规模可观，但终究是有限的样本。为了让模型学习到更广泛的信道分布，从而在未见过的场景中表现更好，可以训练一个生成模型来合成新的、统计特性逼真的信道数据。

* **模型选择**：**条件生成对抗网络（cGAN）** 是一个理想的选择 25。可以利用场景编号（如场景X=1, 2, 3）或从信道中提取的关键物理参数（如莱斯因子、延迟扩展）作为条件输入。cGAN的生成器将学习生成在统计上与真实信道数据无法区分的信道矩阵  
  H。  
* **流程**：首先，在官方提供的CompetitionData上离线训练cGAN。训练完成后，利用其生成器网络创造一个规模远大于原始数据集的合成信道库。最后，在真实数据与合成数据的混合集上训练SVDNet主模型。这种方法已被证明在真实数据有限或存在域偏移的情况下，能显著提升模型的性能和泛化能力 25。虽然扩散模型（Diffusion Models）是更前沿、更强大的生成模型，但其训练过程可能更为复杂 28，cGAN在当前任务中已足够有效且更易实现。

## **6\. 整合优化方案：SVDNet v2.0 蓝图与实施路径**

### **6.1. SVDNet v2.0 架构总览**

综合以上所有优化点，SVDNet v2.0的最终架构和处理流程如下：

* **输入**：形状为$(N\_{\\text{samp}}, 2, M, N)$的复数信道张量。  
* **骨干网络**：采用U-Net编码器-解码器结构，编码器部分使用**MobileNetV3-Large**骨干网络。  
* **预测头**：从解码器的最终输出层分出三个独立的预测头。  
  * **奇异值头 (s-Head)**：通过简单的卷积层、线性层和ReLU激活函数，直接回归预测R个非负奇异值。  
  * **左/右奇异矩阵头 (U-Head & V-Head)**：分别输出一个向量，该向量被送入专属的**Householder参数化层**，确定性地生成M×R和N×R的酉矩阵U和V。

### **6.2. SVDNet v2.0 训练与推理流程**

* **训练阶段**：  
  1. **数据准备**：结合离线的cGAN合成数据与在线的物理先验变换，对官方训练数据进行充分增强。  
  2. **损失函数**：采用**不确定性加权损失**，自动、动态地平衡重构损失与（辅助的）正交性损失。  
  3. **学习率调度器**：使用**循环余弦退火**（Cyclic Cosine Annealing）学习率调度策略。  
  4. **模型集成**：在每个学习率周期的末端（学习率最低点）保存模型权重，作为快照。

### **6.3. 更新后的实施路线图与风险规避**

* **阶段一（核心组件开发）**：实现并单元测试MobileNetV3-UNet架构、Householder参数化层以及不确定性加权损失模块。  
* **阶段二（生成模型训练）**：独立训练用于信道数据合成的cGAN模型。  
* **阶段三（主模型训练）**：使用完整的训练流程（包括数据增强和动态损失）训练SVDNet v2.0。精调关键超参数，如快照数量、学习率范围、初始不确定性权重等。  
* **风险与规避措施**：  
  * **风险**：Householder参数化层的并行化实现（FastH）较为复杂。  
  * **规避**：首先实现一个功能正确的串行版本用于验证，然后根据FastH 12 的原理逐步进行并行化优化。  
  * **风险**：cGAN训练不稳定。  
  * **规避**：采用成熟的GAN训练技巧（如谱归一化、双时间尺度更新规则等），并从简单的生成器/判别器架构开始。  
  * **风险**：几何中位数计算可能较慢。  
  * **规避**：优先使用geomstats 37 等优化库。由于该计算仅在推理的最后阶段对每个样本执行一次，其总计算开销大概率在可接受范围内。

## **7\. 结论：通往最优解的统一路径**

本报告提出的SVDNet v2.0优化方案，并非对原有方案的零散修补，而是一次系统性的、基于第一性原理的重构。它通过一系列相互协同的先进技术，构建了一个全面而强大的解决方案，系统性地应对了竞赛的核心挑战。该方案的优越性体现在，它将以下要素无缝地统一在一个框架内：

* **架构效率**：通过用MobileNetV3替换ConvNeXt，更精准地匹配了任务的计算需求，旨在AE-MACs竞争中获得战略优势。  
* **几何与数值鲁棒性**：通过引入基于Householder变换的参数化，从根本上提升了酉矩阵生成的数值稳定性与计算效率。  
* **原则性与自适应优化**：通过采用不确定性加权损失，将手动调参的启发式过程转变为一个自动化的、有理论依据的动态平衡过程。  
* **最大化的数据利用与泛化**：通过结合物理先验与生成式模型的数据增强策略，最大限度地挖掘了数据价值，提升了模型对未知环境的适应能力。

综上所述，SVDNet v2.0方案在理论的严谨性、架构的先进性、策略的有效性和实施的可行性上均达到了高度统一。它为在本次“AI使能的无线鲁棒SVD算子”挑战赛中取得卓越成绩，提供了一条清晰、可靠且极具潜力的技术路径。

#### **引用的著作**

1. 优化方案，追求最佳性能\_.docx  
2. Which Backbone to Use: A Resource-efficient Domain Specific Comparison for Computer Vision \- arXiv, 访问时间为 七月 31, 2025， [https://arxiv.org/html/2406.05612v1](https://arxiv.org/html/2406.05612v1)  
3. Performance comparison based on NFNet, EfficientNet, and ConvNeXt models | Download Scientific Diagram \- ResearchGate, 访问时间为 七月 31, 2025， [https://www.researchgate.net/figure/Performance-comparison-based-on-NFNet-EfficientNet-and-ConvNeXt-models\_tbl2\_389057773](https://www.researchgate.net/figure/Performance-comparison-based-on-NFNet-EfficientNet-and-ConvNeXt-models_tbl2_389057773)  
4. Review — EfficientNetV2: Smaller Models and Faster Training | by Sik-Ho Tsang \- Medium, 访问时间为 七月 31, 2025， [https://medium.com/aiguys/review-efficientnetv2-smaller-models-and-faster-training-47d4215dcdfb](https://medium.com/aiguys/review-efficientnetv2-smaller-models-and-faster-training-47d4215dcdfb)  
5. On the Efficieny of Convolutional Neural Networks \- arXiv, 访问时间为 七月 31, 2025， [https://arxiv.org/pdf/2404.03617](https://arxiv.org/pdf/2404.03617)  
6. \[1905.02244\] Searching for MobileNetV3 \- ar5iv, 访问时间为 七月 31, 2025， [https://ar5iv.labs.arxiv.org/html/1905.02244](https://ar5iv.labs.arxiv.org/html/1905.02244)  
7. Searching for MobileNetV3, 访问时间为 七月 31, 2025， [https://arxiv.org/pdf/1905.02244](https://arxiv.org/pdf/1905.02244)  
8. \[1905.02244\] Searching for MobileNetV3 \- arXiv, 访问时间为 七月 31, 2025， [https://arxiv.org/abs/1905.02244](https://arxiv.org/abs/1905.02244)  
9. Comparative Analysis of Lightweight Deep Learning Models for Memory-Constrained Devices \- arXiv, 访问时间为 七月 31, 2025， [https://arxiv.org/html/2505.03303v1](https://arxiv.org/html/2505.03303v1)  
10. ConVision Benchmark: A Contemporary Framework to Benchmark CNN and ViT Models \- MDPI, 访问时间为 七月 31, 2025， [https://www.mdpi.com/2673-2688/5/3/56](https://www.mdpi.com/2673-2688/5/3/56)  
11. 3.4. Orthogonal Matrices \- UCSB Math, 访问时间为 七月 31, 2025， [https://web.math.ucsb.edu/\~molera/math104b/lecturenotes/chapter%208/demmel\_sec\_3\_4\_qr\_householder.pdf](https://web.math.ucsb.edu/~molera/math104b/lecturenotes/chapter%208/demmel_sec_3_4_qr_householder.pdf)  
12. Faster Orthogonal Parameterization with ... \- OpenReview, 访问时间为 七月 31, 2025， [https://openreview.net/pdf/34f2432bc5595a7847d320ee3071b325d2a85693.pdf](https://openreview.net/pdf/34f2432bc5595a7847d320ee3071b325d2a85693.pdf)  
13. Multi-Task Learning Using Uncertainty to Weigh Losses for Scene Geometry and Semantics \- CVF Open Access, 访问时间为 七月 31, 2025， [https://openaccess.thecvf.com/content\_cvpr\_2018/papers/Kendall\_Multi-Task\_Learning\_Using\_CVPR\_2018\_paper.pdf](https://openaccess.thecvf.com/content_cvpr_2018/papers/Kendall_Multi-Task_Learning_Using_CVPR_2018_paper.pdf)  
14. Multi-Task Learning Using Uncertainty to Weigh Losses for Scene Geometry and Semantics, 访问时间为 七月 31, 2025， [https://paperswithcode.com/paper/multi-task-learning-using-uncertainty-to](https://paperswithcode.com/paper/multi-task-learning-using-uncertainty-to)  
15. SuperFreeMan-CXM/MTL-Using-Uncertainty-to-Weigh-Losses \- GitHub, 访问时间为 七月 31, 2025， [https://github.com/CXMANDTXW/MTL-Using-Uncertainty-to-Weigh-Losses](https://github.com/CXMANDTXW/MTL-Using-Uncertainty-to-Weigh-Losses)  
16. Mikoto10032/AutomaticWeightedLoss: Multi-task learning using uncertainty to weigh losses for scene geometry and semantics, Auxiliary Tasks in Multi-task Learning \- GitHub, 访问时间为 七月 31, 2025， [https://github.com/Mikoto10032/AutomaticWeightedLoss](https://github.com/Mikoto10032/AutomaticWeightedLoss)  
17. How to learn the weights between two losses? \- PyTorch Forums, 访问时间为 七月 31, 2025， [https://discuss.pytorch.org/t/how-to-learn-the-weights-between-two-losses/39681](https://discuss.pytorch.org/t/how-to-learn-the-weights-between-two-losses/39681)  
18. Dynamic Restrained Uncertainty Weighting Loss for Multitask Learning of Vocal Expression \- arXiv, 访问时间为 七月 31, 2025， [https://arxiv.org/pdf/2206.11049](https://arxiv.org/pdf/2206.11049)  
19. Analytical Uncertainty-Based Loss Weighting in Multi-Task Learning \- arXiv, 访问时间为 七月 31, 2025， [https://arxiv.org/html/2408.07985v1](https://arxiv.org/html/2408.07985v1)  
20. Simple and Effective Augmentation Methods for CSI Based Indoor Localization \- arXiv, 访问时间为 七月 31, 2025， [https://arxiv.org/pdf/2211.10790](https://arxiv.org/pdf/2211.10790)  
21. Deep Learning-Based Robust Channel Estimation for MIMO IoT Systems \- ResearchGate, 访问时间为 七月 31, 2025， [https://www.researchgate.net/publication/374771737\_Deep\_Learning-Based\_Robust\_Channel\_Estimation\_for\_MIMO\_IoT\_Systems](https://www.researchgate.net/publication/374771737_Deep_Learning-Based_Robust_Channel_Estimation_for_MIMO_IoT_Systems)  
22. Deep-Learning-Based Carrier Frequency Offset Estimation and Its Cross-Evaluation in Multiple-Channel Models \- MDPI, 访问时间为 七月 31, 2025， [https://www.mdpi.com/2078-2489/14/2/98](https://www.mdpi.com/2078-2489/14/2/98)  
23. Deep Learning for MIMO Channel Estimation: Interpretation, Performance, and Comparison \- arXiv, 访问时间为 七月 31, 2025， [https://arxiv.org/pdf/1911.01918](https://arxiv.org/pdf/1911.01918)  
24. Data Augmentation of Bridging the Delay Gap for DL-based Massive MIMO CSI Feedback, 访问时间为 七月 31, 2025， [https://www.researchgate.net/publication/372827669\_Data\_Augmentation\_of\_Bridging\_the\_Delay\_Gap\_for\_DL-based\_Massive\_MIMO\_CSI\_Feedback](https://www.researchgate.net/publication/372827669_Data_Augmentation_of_Bridging_the_Delay_Gap_for_DL-based_Massive_MIMO_CSI_Feedback)  
25. CGAN-Based Data Augmentation for Enhanced Channel Prediction in Massive MIMO under Subway Tunnels \- ResearchGate, 访问时间为 七月 31, 2025， [https://www.researchgate.net/publication/390579234\_CGAN-Based\_Data\_Augmentation\_for\_Enhanced\_Channel\_Prediction\_in\_Massive\_MIMO\_under\_Subway\_Tunnels](https://www.researchgate.net/publication/390579234_CGAN-Based_Data_Augmentation_for_Enhanced_Channel_Prediction_in_Massive_MIMO_under_Subway_Tunnels)  
26. \[PDF\] MIMO-GAN: Generative MIMO Channel Modeling \- Semantic Scholar, 访问时间为 七月 31, 2025， [https://www.semanticscholar.org/paper/4437e870f338bfc6f4b2d8408680a5f234a619d4](https://www.semanticscholar.org/paper/4437e870f338bfc6f4b2d8408680a5f234a619d4)  
27. \[2203.08588\] MIMO-GAN: Generative MIMO Channel Modeling \- arXiv, 访问时间为 七月 31, 2025， [https://arxiv.org/abs/2203.08588](https://arxiv.org/abs/2203.08588)  
28. Generative Diffusion Model-based Variational Inference for MIMO Channel Estimation | Request PDF \- ResearchGate, 访问时间为 七月 31, 2025， [https://www.researchgate.net/publication/390280078\_Generative\_Diffusion\_Model-based\_Variational\_Inference\_for\_MIMO\_Channel\_Estimation](https://www.researchgate.net/publication/390280078_Generative_Diffusion_Model-based_Variational_Inference_for_MIMO_Channel_Estimation)  
29. Learning Loss for Test-Time Augmentation \- NIPS, 访问时间为 七月 31, 2025， [https://papers.neurips.cc/paper\_files/paper/2020/file/2ba596643cbbbc20318224181fa46b28-Paper.pdf](https://papers.neurips.cc/paper_files/paper/2020/file/2ba596643cbbbc20318224181fa46b28-Paper.pdf)  
30. Understanding Test-Time Augmentation \- arXiv, 访问时间为 七月 31, 2025， [https://arxiv.org/html/2402.06892v1](https://arxiv.org/html/2402.06892v1)  
31. Feature Augmentation Based Test-Time Adaptation \- CVF Open Access, 访问时间为 七月 31, 2025， [https://openaccess.thecvf.com/content/WACV2025/papers/Cho\_Feature\_Augmentation\_Based\_Test-Time\_Adaptation\_WACV\_2025\_paper.pdf](https://openaccess.thecvf.com/content/WACV2025/papers/Cho_Feature_Augmentation_Based_Test-Time_Adaptation_WACV_2025_paper.pdf)  
32. Cyclic Test Time Augmentation with Entropy Weight Method \- Proceedings of Machine Learning Research, 访问时间为 七月 31, 2025， [https://proceedings.mlr.press/v180/chun22a/chun22a.pdf](https://proceedings.mlr.press/v180/chun22a/chun22a.pdf)  
33. The Geometric Median on Riemannian Manifolds with Application to Robust Atlas Estimation | Request PDF \- ResearchGate, 访问时间为 七月 31, 2025， [https://www.researchgate.net/publication/23571442\_The\_Geometric\_Median\_on\_Riemannian\_Manifolds\_with\_Application\_to\_Robust\_Atlas\_Estimation](https://www.researchgate.net/publication/23571442_The_Geometric_Median_on_Riemannian_Manifolds_with_Application_to_Robust_Atlas_Estimation)  
34. Robust statistics on Riemannian manifolds via the geometric median \- ResearchGate, 访问时间为 七月 31, 2025， [https://www.researchgate.net/publication/221364773\_Robust\_statistics\_on\_Riemannian\_manifolds\_via\_the\_geometric\_median](https://www.researchgate.net/publication/221364773_Robust_statistics_on_Riemannian_manifolds_via_the_geometric_median)  
35. The Geometric Median on Riemannian Manifolds with Application to Robust Atlas Estimation \- PMC \- PubMed Central, 访问时间为 七月 31, 2025， [https://pmc.ncbi.nlm.nih.gov/articles/PMC2735114/](https://pmc.ncbi.nlm.nih.gov/articles/PMC2735114/)  
36. The geometric median on Riemannian manifolds with application to robust atlas estimation \- PubMed, 访问时间为 七月 31, 2025， [https://pubmed.ncbi.nlm.nih.gov/19056498/](https://pubmed.ncbi.nlm.nih.gov/19056498/)  
37. Geomstats: A Python Package for Riemannian Geometry in Machine Learning, 访问时间为 七月 31, 2025， [https://www.jmlr.org/papers/volume21/19-027/19-027.pdf](https://www.jmlr.org/papers/volume21/19-027/19-027.pdf)  
38. Pymanopt: A Python Toolbox for Manifold Optimization using Automatic Differentiation | Request PDF \- ResearchGate, 访问时间为 七月 31, 2025， [https://www.researchgate.net/publication/301881070\_Pymanopt\_A\_Python\_Toolbox\_for\_Manifold\_Optimization\_using\_Automatic\_Differentiation](https://www.researchgate.net/publication/301881070_Pymanopt_A_Python_Toolbox_for_Manifold_Optimization_using_Automatic_Differentiation)